version: "3.7"

services:
  app:
    build:
      args:
        user: developer
        uid: 1000
      context: ./
      dockerfile: Dockerfile
    image: app
    container_name: viking-app
    restart: unless-stopped
    environment:
      VIRTUAL_HOST: laravel.test
    working_dir: /var/www/
    volumes:
      - ./../:/var/www
      - ~/.ssh:/root/.ssh
    depends_on:
      - db
    networks:
      - laravel
  db:
    image: mysql:8.0
    container_name: viking-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: viking
      MYSQL_ROOT_PASSWORD: viking
    volumes:
      - ./mysql/data:/var/lib/mysql
      - ./mysql/logs:/var/log/mysql
      - ./mysql/ql:/docker-entrypoint-initdb.d
    networks:
      - laravel
  redis:
    image: redis:latest
    restart: always
    ports:
      - "6379:6379"
    networks:
      - laravel
  nginx:
    image: nginx:alpine
    container_name: viking-nginx
    restart: unless-stopped
    ports:
      - 80:80
    volumes:
      - ./../:/var/www
      - ./nginx:/etc/nginx/conf.d
    networks:
      - laravel
networks:
  laravel:
    driver: bridge